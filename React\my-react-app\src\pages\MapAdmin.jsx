import React, { useState, useContext } from 'react';
import { LanguageContext } from '../context/LanguageContext';
import Map from '../components/Map';
import { getAllLocations, setCurrentLocation, addNewLocation, CURRENT_LOCATION } from '../config/mapConfig';

export default function MapAdmin() {
  const { lang } = useContext(LanguageContext);
  const [selectedLocation, setSelectedLocation] = useState('default');
  const [customLocation, setCustomLocation] = useState({
    name: '',
    nameAr: '',
    latitude: '',
    longitude: '',
    address: '',
    addressEn: '',
    zoom: 15
  });
  const [showCustomForm, setShowCustomForm] = useState(false);

  const texts = {
    ar: {
      title: "إدارة الخريطة",
      currentLocation: "الموقع الحالي",
      selectLocation: "اختر موقعاً محدداً مسبقاً",
      customLocation: "إضافة موقع مخصص",
      locationName: "اسم المكان",
      locationNameAr: "اسم المكان بالعربية",
      latitude: "خط العرض",
      longitude: "خط الطول",
      address: "العنوان",
      addressEn: "العنوان بالإنجليزية",
      zoom: "مستوى التكبير",
      apply: "تطبيق",
      addCustom: "إضافة موقع مخصص",
      cancel: "إلغاء",
      instructions: "تعليمات",
      instructionsText: "لتخصيص موقع الخريطة، يمكنك اختيار موقع محدد مسبقاً أو إضافة موقع جديد بإدخال الإحداثيات.",
      getCoordinates: "للحصول على الإحداثيات: اذهب إلى خرائط جوجل، انقر بالزر الأيمن على الموقع، واختر 'ما هذا المكان؟'"
    },
    en: {
      title: "Map Management",
      currentLocation: "Current Location",
      selectLocation: "Select a predefined location",
      customLocation: "Add custom location",
      locationName: "Location Name",
      locationNameAr: "Location Name in Arabic",
      latitude: "Latitude",
      longitude: "Longitude",
      address: "Address",
      addressEn: "Address in English",
      zoom: "Zoom Level",
      apply: "Apply",
      addCustom: "Add Custom Location",
      cancel: "Cancel",
      instructions: "Instructions",
      instructionsText: "To customize the map location, you can select a predefined location or add a new one by entering coordinates.",
      getCoordinates: "To get coordinates: Go to Google Maps, right-click on the location, and select 'What's here?'"
    }
  };

  const t = texts[lang] || texts.ar;

  const handleLocationChange = (locationKey) => {
    if (setCurrentLocation(locationKey)) {
      setSelectedLocation(locationKey);
      alert(lang === 'ar' ? 'تم تحديث الموقع بنجاح!' : 'Location updated successfully!');
      // إعادة تحميل الصفحة لتطبيق التغيير
      setTimeout(() => window.location.reload(), 1000);
    }
  };

  const handleCustomLocationSubmit = (e) => {
    e.preventDefault();
    
    if (!customLocation.name || !customLocation.latitude || !customLocation.longitude) {
      alert(lang === 'ar' ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields');
      return;
    }

    const newLocationKey = `custom_${Date.now()}`;
    const locationData = {
      latitude: parseFloat(customLocation.latitude),
      longitude: parseFloat(customLocation.longitude),
      zoom: parseInt(customLocation.zoom) || 15,
      address: customLocation.address || customLocation.name,
      addressEn: customLocation.addressEn || customLocation.name,
      placeName: customLocation.name,
      placeNameAr: customLocation.nameAr || customLocation.name
    };

    addNewLocation(newLocationKey, locationData);
    handleLocationChange(newLocationKey);
  };

  const handleInputChange = (field, value) => {
    setCustomLocation(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="page map-admin" dir={lang === 'ar' ? 'rtl' : 'ltr'} style={{ padding: '2rem' }}>
      <h1>{t.title}</h1>

      {/* تعليمات */}
      <div style={{
        background: 'var(--bg-secondary)',
        padding: '1.5rem',
        borderRadius: 'var(--radius-lg)',
        marginBottom: '2rem',
        border: '1px solid var(--bg-tertiary)'
      }}>
        <h3>{t.instructions}</h3>
        <p style={{ marginBottom: '0.5rem' }}>{t.instructionsText}</p>
        <p style={{ fontSize: '0.9rem', color: 'var(--text-secondary)' }}>
          {t.getCoordinates}
        </p>
      </div>

      {/* الموقع الحالي */}
      <div style={{ marginBottom: '2rem' }}>
        <h3>{t.currentLocation}</h3>
        <div style={{
          background: 'var(--bg-primary)',
          padding: '1rem',
          borderRadius: 'var(--radius-md)',
          border: '1px solid var(--bg-tertiary)',
          marginBottom: '1rem'
        }}>
          <p><strong>{lang === 'ar' ? 'الاسم:' : 'Name:'}</strong> {lang === 'ar' ? CURRENT_LOCATION.placeNameAr : CURRENT_LOCATION.placeName}</p>
          <p><strong>{lang === 'ar' ? 'العنوان:' : 'Address:'}</strong> {lang === 'ar' ? CURRENT_LOCATION.address : CURRENT_LOCATION.addressEn}</p>
          <p><strong>{lang === 'ar' ? 'الإحداثيات:' : 'Coordinates:'}</strong> {CURRENT_LOCATION.latitude}, {CURRENT_LOCATION.longitude}</p>
        </div>
      </div>

      {/* اختيار موقع محدد مسبقاً */}
      <div style={{ marginBottom: '2rem' }}>
        <h3>{t.selectLocation}</h3>
        <select 
          value={selectedLocation}
          onChange={(e) => handleLocationChange(e.target.value)}
          style={{
            width: '100%',
            padding: '0.75rem',
            borderRadius: 'var(--radius-md)',
            border: '2px solid var(--bg-tertiary)',
            background: 'var(--bg-primary)',
            color: 'var(--text-primary)',
            fontSize: '1rem',
            marginBottom: '1rem'
          }}
        >
          <option value="default">
            {lang === 'ar' ? 'الموقع الافتراضي - بغداد' : 'Default Location - Baghdad'}
          </option>
          {Object.entries(getAllLocations()).map(([key, location]) => {
            if (key === 'default') return null;
            return (
              <option key={key} value={key}>
                {lang === 'ar' ? location.placeNameAr : location.placeName}
              </option>
            );
          })}
        </select>
      </div>

      {/* إضافة موقع مخصص */}
      <div style={{ marginBottom: '2rem' }}>
        <button
          onClick={() => setShowCustomForm(!showCustomForm)}
          style={{
            background: 'var(--primary-gradient)',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: 'var(--radius-md)',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: '600'
          }}
        >
          {showCustomForm ? t.cancel : t.customLocation}
        </button>

        {showCustomForm && (
          <form onSubmit={handleCustomLocationSubmit} style={{
            background: 'var(--bg-primary)',
            padding: '1.5rem',
            borderRadius: 'var(--radius-lg)',
            border: '1px solid var(--bg-tertiary)',
            marginTop: '1rem'
          }}>
            <div style={{ display: 'grid', gap: '1rem', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))' }}>
              <div>
                <label>{t.locationName} *</label>
                <input
                  type="text"
                  value={customLocation.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  required
                  style={{
                    width: '100%',
                    padding: '0.5rem',
                    borderRadius: 'var(--radius-md)',
                    border: '1px solid var(--bg-tertiary)',
                    background: 'var(--bg-secondary)'
                  }}
                />
              </div>

              <div>
                <label>{t.locationNameAr}</label>
                <input
                  type="text"
                  value={customLocation.nameAr}
                  onChange={(e) => handleInputChange('nameAr', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.5rem',
                    borderRadius: 'var(--radius-md)',
                    border: '1px solid var(--bg-tertiary)',
                    background: 'var(--bg-secondary)'
                  }}
                />
              </div>

              <div>
                <label>{t.latitude} *</label>
                <input
                  type="number"
                  step="any"
                  value={customLocation.latitude}
                  onChange={(e) => handleInputChange('latitude', e.target.value)}
                  required
                  placeholder="33.3152"
                  style={{
                    width: '100%',
                    padding: '0.5rem',
                    borderRadius: 'var(--radius-md)',
                    border: '1px solid var(--bg-tertiary)',
                    background: 'var(--bg-secondary)'
                  }}
                />
              </div>

              <div>
                <label>{t.longitude} *</label>
                <input
                  type="number"
                  step="any"
                  value={customLocation.longitude}
                  onChange={(e) => handleInputChange('longitude', e.target.value)}
                  required
                  placeholder="44.3661"
                  style={{
                    width: '100%',
                    padding: '0.5rem',
                    borderRadius: 'var(--radius-md)',
                    border: '1px solid var(--bg-tertiary)',
                    background: 'var(--bg-secondary)'
                  }}
                />
              </div>

              <div>
                <label>{t.address}</label>
                <input
                  type="text"
                  value={customLocation.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.5rem',
                    borderRadius: 'var(--radius-md)',
                    border: '1px solid var(--bg-tertiary)',
                    background: 'var(--bg-secondary)'
                  }}
                />
              </div>

              <div>
                <label>{t.addressEn}</label>
                <input
                  type="text"
                  value={customLocation.addressEn}
                  onChange={(e) => handleInputChange('addressEn', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.5rem',
                    borderRadius: 'var(--radius-md)',
                    border: '1px solid var(--bg-tertiary)',
                    background: 'var(--bg-secondary)'
                  }}
                />
              </div>
            </div>

            <button
              type="submit"
              style={{
                background: 'var(--accent-gradient)',
                color: 'white',
                border: 'none',
                padding: '0.75rem 2rem',
                borderRadius: 'var(--radius-md)',
                cursor: 'pointer',
                fontSize: '1rem',
                fontWeight: '600',
                marginTop: '1rem'
              }}
            >
              {t.addCustom}
            </button>
          </form>
        )}
      </div>

      {/* معاينة الخريطة */}
      <div>
        <h3>{lang === 'ar' ? 'معاينة الخريطة' : 'Map Preview'}</h3>
        <Map showTitle={false} height="300px" />
      </div>
    </div>
  );
}
