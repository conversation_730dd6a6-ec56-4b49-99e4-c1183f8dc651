import React, { useContext, useState } from 'react';
import { LanguageContext } from '../context/LanguageContext';
import { CURRENT_LOCATION, MAP_SETTINGS, getAllLocations, setCurrentLocation } from '../config/mapConfig';

export default function Map({
  latitude = CURRENT_LOCATION.latitude,
  longitude = CURRENT_LOCATION.longitude,
  zoom = CURRENT_LOCATION.zoom,
  address = CURRENT_LOCATION.address,
  addressEn = CURRENT_LOCATION.addressEn,
  placeName = CURRENT_LOCATION.placeName,
  placeNameAr = CURRENT_LOCATION.placeNameAr,
  height = MAP_SETTINGS.defaultHeight,
  showTitle = true,
  showLocationSelector = false
}) {
  const { lang } = useContext(LanguageContext);
  const [selectedLocation, setSelectedLocation] = useState('default');

  // إنشاء رابط الخريطة
  const mapUrl = `https://www.google.com/maps/embed/v1/place?key=AIzaSyBFw0Qbyq9zTFTd-tUY6dw901SwHHqfeWM&q=${latitude},${longitude}&zoom=${zoom}&language=${lang}`;
  
  // رابط بديل باستخدام العنوان
  const mapUrlByAddress = `https://www.google.com/maps/embed/v1/search?key=AIzaSyBFw0Qbyq9zTFTd-tUY6dw901SwHHqfeWM&q=${encodeURIComponent(lang === 'ar' ? address : addressEn)}&language=${lang}`;

  const t = MAP_SETTINGS.texts[lang] || MAP_SETTINGS.texts.ar;

  // دالة لتغيير الموقع
  const handleLocationChange = (locationKey) => {
    if (setCurrentLocation(locationKey)) {
      setSelectedLocation(locationKey);
      // إعادة تحميل الصفحة لتطبيق التغيير (يمكن تحسينها لاحقاً)
      window.location.reload();
    }
  };

  // رابط لفتح الخريطة في تطبيق منفصل
  const openInGoogleMaps = () => {
    const url = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
    window.open(url, '_blank');
  };

  return (
    <section className="map-section" data-aos="zoom-in">
      {showTitle && <h2>{t.title}</h2>}

      {/* مكون اختيار الموقع */}
      {showLocationSelector && (
        <div className="location-selector" style={{
          marginBottom: '1.5rem',
          textAlign: 'center'
        }}>
          <label style={{
            display: 'block',
            marginBottom: '0.5rem',
            fontWeight: '600',
            color: 'var(--text-primary)'
          }}>
            {t.selectLocation}:
          </label>
          <select
            value={selectedLocation}
            onChange={(e) => handleLocationChange(e.target.value)}
            style={{
              padding: '0.5rem 1rem',
              borderRadius: 'var(--radius-md)',
              border: '2px solid var(--bg-tertiary)',
              background: 'var(--bg-primary)',
              color: 'var(--text-primary)',
              fontSize: '0.9rem',
              cursor: 'pointer'
            }}
          >
            <option value="default">
              {lang === 'ar' ? 'الموقع الافتراضي' : 'Default Location'}
            </option>
            {Object.entries(getAllLocations()).map(([key, location]) => {
              if (key === 'default') return null;
              return (
                <option key={key} value={key}>
                  {lang === 'ar' ? location.placeNameAr : location.placeName}
                </option>
              );
            })}
          </select>
        </div>
      )}
      
      <div className="map-container">
        <iframe
          title={lang === 'ar' ? placeNameAr : placeName}
          src={mapUrl}
          width="100%"
          height={height}
          style={{ border: 0, borderRadius: '12px' }}
          allowFullScreen=""
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
          onError={(e) => {
            console.error('Map loading error:', e);
            // يمكن إضافة fallback هنا
          }}
        />
        
        {/* زر لفتح الخريطة في تطبيق منفصل */}
        <div className="map-actions" style={{ 
          marginTop: '1rem', 
          textAlign: 'center' 
        }}>
          <button 
            onClick={openInGoogleMaps}
            className="map-button"
            style={{
              background: 'var(--primary-gradient)',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: 'var(--radius-md)',
              cursor: 'pointer',
              fontSize: '0.9rem',
              fontWeight: '600',
              transition: 'var(--transition-normal)',
              boxShadow: 'var(--shadow-md)'
            }}
            onMouseEnter={(e) => {
              e.target.style.transform = 'translateY(-2px)';
              e.target.style.boxShadow = 'var(--shadow-lg)';
            }}
            onMouseLeave={(e) => {
              e.target.style.transform = 'translateY(0)';
              e.target.style.boxShadow = 'var(--shadow-md)';
            }}
          >
            📍 {t.openInMaps}
          </button>
        </div>

        {/* معلومات الموقع */}
        <div className="location-info" style={{
          marginTop: '1rem',
          padding: '1rem',
          background: 'var(--bg-primary)',
          borderRadius: 'var(--radius-md)',
          border: '1px solid var(--bg-tertiary)',
          textAlign: 'center'
        }}>
          <h4 style={{ 
            margin: '0 0 0.5rem 0',
            color: 'var(--text-primary)',
            fontSize: '1.1rem'
          }}>
            {lang === 'ar' ? placeNameAr : placeName}
          </h4>
          <p style={{ 
            margin: 0,
            color: 'var(--text-secondary)',
            fontSize: '0.9rem'
          }}>
            {lang === 'ar' ? address : addressEn}
          </p>
        </div>
      </div>
    </section>
  );
}

// دالة مساعدة لتحديث إعدادات الخريطة
export const updateMapConfig = (newConfig) => {
  Object.assign(MAP_CONFIG, newConfig);
};

// دالة للحصول على إحداثيات من عنوان (تتطلب API key)
export const getCoordinatesFromAddress = async (address) => {
  try {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=YOUR_API_KEY`
    );
    const data = await response.json();
    
    if (data.results && data.results.length > 0) {
      const location = data.results[0].geometry.location;
      return {
        latitude: location.lat,
        longitude: location.lng
      };
    }
    throw new Error('Address not found');
  } catch (error) {
    console.error('Geocoding error:', error);
    return null;
  }
};
