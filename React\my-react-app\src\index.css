/* ===== CSS Variables for Luxury Design ===== */
:root {
  /* Primary Colors - Luxury Gold & Deep Blue */
  --primary-gradient: linear-gradient(135deg, #d4af37 0%, #1e3a8a 50%, #0f172a 100%);
  --primary-color: #d4af37;
  --primary-dark: #b8941f;
  --secondary-color: #1e3a8a;
  --secondary-dark: #1e40af;

  /* Accent Colors - Elegant Purple & Rose Gold */
  --accent-color: #e879f9;
  --accent-gradient: linear-gradient(135deg, #fbbf24 0%, #d946ef 50%, #7c3aed 100%);
  --luxury-gradient: linear-gradient(135deg, #ffd700 0%, #ff6b6b 50%, #4ecdc4 100%);

  /* Neutral Colors - Sophisticated Whites & Grays */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #e2e8f0;
  --bg-luxury: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  --text-primary: #0f172a;
  --text-secondary: #334155;
  --text-muted: #64748b;

  /* Luxury Shadows with Gold Tints */
  --shadow-sm: 0 1px 3px 0 rgba(212, 175, 55, 0.15), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(212, 175, 55, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(212, 175, 55, 0.25), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(212, 175, 55, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-luxury: 0 8px 32px rgba(212, 175, 55, 0.3), 0 4px 16px rgba(30, 58, 138, 0.2);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Typography */
  --font-primary: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-secondary: 'Cairo', 'Segoe UI', sans-serif;

  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* Dark theme variables - Luxury Dark Mode */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-luxury: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  --text-primary: #f8fafc;
  --text-secondary: #e2e8f0;
  --text-muted: #94a3b8;

  /* Dark mode specific gradients */
  --primary-gradient: linear-gradient(135deg, #fbbf24 0%, #3b82f6 50%, #1e40af 100%);
  --accent-gradient: linear-gradient(135deg, #fbbf24 0%, #a855f7 50%, #6366f1 100%);
  --luxury-gradient: linear-gradient(135deg, #ffd700 0%, #ff6b6b 50%, #4ecdc4 100%);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-primary);
  background: var(--bg-luxury);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Luxury headings */
h1, h2, h3, h4, h5, h6 {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Dark mode text adjustments */
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
  background: var(--luxury-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* ===== Luxury Navbar ===== */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--bg-tertiary);
  color: var(--text-primary);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-luxury);
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-luxury);
}

/* Dark mode navbar styles */
[data-theme="dark"] .navbar {
  background: rgba(15, 23, 42, 0.95);
  border-bottom: 1px solid var(--bg-tertiary);
  box-shadow: 0 2px 20px rgba(212, 175, 55, 0.2);
}

[data-theme="dark"] .navbar.scrolled {
  background: rgba(15, 23, 42, 0.98);
  box-shadow: var(--shadow-luxury);
}

.navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  font-weight: 700;
  font-size: 1.5rem;
  transition: var(--transition-fast);
}

.logo:hover {
  transform: scale(1.05);
}

.logo-icon {
  font-size: 1.75rem;
  filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
}

.logo-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-links {
  list-style: none;
  display: flex;
  gap: 0.5rem;
  align-items: center;
  margin: 0;
  padding: 0;
}

.nav-links li a {
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
  padding: 0.75rem 1.25rem;
  border-radius: var(--radius-lg);
  transition: var(--transition-fast);
  position: relative;
  display: block;
}

.nav-links li a::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--primary-gradient);
  transition: var(--transition-fast);
  transform: translateX(-50%);
}

.nav-links li a:hover::before,
.nav-links li a.active::before {
  width: 80%;
}

.nav-links li a:hover {
  background: var(--bg-tertiary);
  transform: translateY(-1px);
}

.nav-links li a.active {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-md);
}

.controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.lang-toggle,
.theme-toggle {
  background: var(--bg-tertiary);
  border: none;
  padding: 0.6rem 1rem;
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-weight: 600;
  color: var(--text-primary);
  transition: var(--transition-fast);
  font-size: 0.875rem;
  min-width: 3rem;
  box-shadow: var(--shadow-sm);
}

.lang-toggle:hover,
.theme-toggle:hover {
  background: var(--primary-gradient);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-luxury);
}

/* Dark mode styles for control buttons */
[data-theme="dark"] .lang-toggle,
[data-theme="dark"] .theme-toggle {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid rgba(212, 175, 55, 0.2);
}

[data-theme="dark"] .lang-toggle:hover,
[data-theme="dark"] .theme-toggle:hover {
  background: var(--primary-gradient);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-luxury);
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.mobile-menu-toggle span {
  width: 24px;
  height: 3px;
  background: var(--text-primary);
  border-radius: 2px;
  transition: var(--transition-fast);
}

.mobile-menu-toggle:hover {
  background: var(--bg-tertiary);
}

/* Mobile menu animations */
.navbar.mobile-open .mobile-menu-toggle span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.navbar.mobile-open .mobile-menu-toggle span:nth-child(2) {
  opacity: 0;
}

.navbar.mobile-open .mobile-menu-toggle span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

main {
  flex: 1;
  padding: 2rem 1rem;
  max-width: 1400px;
  margin: auto;
}

.hero {
  background: var(--primary-gradient);
  padding: 5rem 2rem;
  color: white;
  text-align: center;
  border-radius: var(--radius-2xl);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.hero-content {
  position: relative;
  z-index: 1;
}

.hero-content h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  margin-bottom: 1.5rem;
  font-weight: 800;
  line-height: 1.2;
  background: var(--luxury-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(212, 175, 55, 0.3);
}

.hero-content p {
  font-size: clamp(1.1rem, 2.5vw, 1.4rem);
  margin-bottom: 2rem;
  opacity: 0.95;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-button {
  background: var(--luxury-gradient);
  padding: 1rem 2.5rem;
  border: none;
  border-radius: var(--radius-xl);
  color: white;
  font-weight: 700;
  font-size: 1.1rem;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-luxury);
  position: relative;
  overflow: hidden;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-luxury), 0 15px 35px rgba(212, 175, 55, 0.4);
  filter: brightness(1.1);
}

.cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: var(--transition-slow);
}

.cta-button:hover::before {
  left: 100%;
}

.page h1 {
  text-align: center;
  margin-bottom: 3rem;
  font-size: clamp(2rem, 4vw, 3rem);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.services-grid,
.projects-grid {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  padding: 2rem 0;
}

.service-card,
.project-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  border: 1px solid var(--bg-tertiary);
  position: relative;
  overflow: hidden;
}

.service-card:hover,
.project-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.service-card::before,
.project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.footer {
  background: var(--text-primary);
  color: var(--bg-primary);
  text-align: center;
  padding: 3rem 2rem 2rem;
  margin-top: auto;
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--primary-gradient);
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.social-links a {
  color: var(--bg-primary);
  text-decoration: none;
  padding: 0.75rem;
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
  background: var(--bg-tertiary);
}

.social-links a:hover {
  background: var(--primary-color);
  transform: translateY(-2px);
}
/* ===== Modern Form Styles ===== */
.page {
  padding: 2rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

input, textarea {
  width: 100%;
  padding: 1rem;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
  border: 2px solid var(--bg-tertiary);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: var(--font-primary);
  transition: var(--transition-fast);
}

input:focus, textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

button {
  padding: 0.75rem 1.5rem;
  background: var(--primary-gradient);
  border: none;
  color: white;
  cursor: pointer;
  border-radius: var(--radius-md);
  font-weight: 600;
  transition: var(--transition-normal);
  font-family: var(--font-primary);
  box-shadow: var(--shadow-md);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-luxury);
  filter: brightness(1.05);
}

/* ===== RTL Support ===== */
[dir="rtl"] {
  direction: rtl;
}

[dir="rtl"] .nav-links {
  flex-direction: row-reverse;
}

/* ===== Luxury Dark Theme Support ===== */
body.dark {
  background: var(--bg-luxury);
  color: var(--text-primary);
}

[data-theme="dark"] body {
  background: var(--bg-luxury);
  color: var(--text-primary);
}

/* ===== Modern Section Styles ===== */
.values-section, .about-preview, .services-preview, .contact-cta {
  padding: 4rem 2rem;
  text-align: center;
}

.service-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin: 2rem 0;
}

.service-cards .card {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-luxury);
  transition: var(--transition-normal);
  border: 1px solid var(--bg-tertiary);
  position: relative;
  overflow: hidden;
}

.service-cards .card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--luxury-gradient);
  opacity: 0;
  transition: var(--transition-normal);
}

.service-cards .card:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-luxury), 0 20px 40px rgba(212, 175, 55, 0.3);
}

.service-cards .card:hover::before {
  opacity: 1;
}

.link-button {
  display: inline-block;
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  background: var(--primary-gradient);
  color: white;
  text-decoration: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  transition: var(--transition-normal);
}

.link-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.contact-cta {
  background: var(--primary-gradient);
  color: white;
  border-radius: var(--radius-2xl);
  margin: 2rem 0;
}


/* ===== Modern Services Page ===== */
.page.services {
  padding: 4rem 2rem 6rem;
  max-width: 1400px;
  margin: 0 auto;
  font-family: var(--font-primary);
  color: var(--text-primary);
  text-align: center;
  background: var(--bg-secondary);
  min-height: 100vh;
  box-sizing: border-box;
}

.services-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  margin-bottom: 1.5rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  user-select: none;
}

.services-subtitle {
  font-size: clamp(1rem, 2vw, 1.3rem);
  color: var(--text-secondary);
  margin-bottom: 4rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2.5rem;
  margin-top: 3rem;
}

/* ===== Modern Service Card ===== */
.service-card {
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: 2.5rem 2rem;
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(20px);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: var(--transition-normal);
  position: relative;
  user-select: none;
  border: 1px solid var(--bg-tertiary);
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.service-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.icon {
  width: 4rem;
  height: 4rem;
  margin-bottom: 1.5rem;
  user-select: none;
  color: var(--primary-color);
  filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
}

.service-card h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.service-card p {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  color: var(--text-secondary);
  min-height: 4rem;
  display: flex;
  align-items: center;
}

/* ===== Modern Learn More Button ===== */
.btn-learn-more {
  background: var(--accent-gradient);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: var(--radius-xl);
  font-weight: 600;
  font-size: 1rem;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-md);
  cursor: pointer;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.btn-learn-more:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-learn-more::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: var(--transition-slow);
}

.btn-learn-more:hover::before {
  left: 100%;
}

/* ===== Modern Modal ===== */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  padding: 1.5rem;
  box-sizing: border-box;
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: 3rem 2.5rem;
  max-width: 600px;
  width: 100%;
  box-shadow: var(--shadow-xl);
  position: relative;
  color: var(--text-primary);
  text-align: center;
  user-select: none;
  border: 1px solid var(--bg-tertiary);
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--bg-tertiary);
  border: none;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  color: var(--text-primary);
  cursor: pointer;
  font-weight: 700;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
}

.modal-close:hover {
  background: var(--primary-color);
  color: white;
  transform: scale(1.1);
}

.modal-icon {
  margin-bottom: 1.5rem;
  width: 5rem;
  height: 5rem;
  margin-left: auto;
  margin-right: auto;
  color: var(--primary-color);
  filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
}

.modal-content h2 {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: 700;
  margin-bottom: 1.5rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modal-content p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
}

/* ===== Modern Responsive Design ===== */
/* ===== Mobile Navigation ===== */
@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: flex;
  }

  .nav-links {
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    flex-direction: column;
    gap: 0;
    padding: 2rem;
    box-shadow: var(--shadow-xl);
    border-top: 1px solid var(--bg-tertiary);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
  }

  .nav-links.mobile-menu-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-links li {
    width: 100%;
  }

  .nav-links li a {
    padding: 1rem;
    text-align: center;
    border-radius: var(--radius-md);
    margin-bottom: 0.5rem;
    display: block;
  }

  .navbar-container {
    padding: 1rem;
  }

  .controls {
    gap: 0.5rem;
  }

  .lang-toggle,
  .theme-toggle {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    min-width: 2.5rem;
  }

  .services-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  .page.services {
    padding: 3rem 1rem 4rem;
  }

  .hero {
    padding: 3rem 1.5rem;
  }

  .btn-learn-more {
    padding: 0.6rem 1.5rem;
    font-size: 0.9rem;
  }

  .modal-content {
    padding: 2rem 1.5rem;
    margin: 1rem;
  }

  .service-cards {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .navbar {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .nav-links {
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
  }

  .nav-links li a {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }

  .logo {
    font-size: 1.5rem;
  }

  .btn-learn-more {
    padding: 0.5rem 1.25rem;
    font-size: 0.85rem;
  }

  .modal-backdrop {
    padding: 1rem;
  }

  .modal-content {
    padding: 1.5rem 1rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .page.services {
    padding: 2rem 1rem 3rem;
  }

  .services-subtitle {
    font-size: 1rem;
    margin-bottom: 2rem;
  }
}

/* ===== Additional Modern Enhancements ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== Smooth Scrolling ===== */
html {
  scroll-behavior: smooth;
}

/* ===== Focus Styles ===== */
*:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* ===== Selection Styles ===== */
::selection {
  background: var(--primary-color);
  color: white;
}

/* ===== Scrollbar Styles ===== */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}


/* ===== Modern Projects Page ===== */
.page.projects {
  padding: 4rem 2rem;
  font-family: var(--font-primary);
  background: var(--bg-secondary);
  color: var(--text-primary);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* ===== Projects Title ===== */
.page.projects > h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  margin-bottom: 3rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  letter-spacing: -0.02em;
  user-select: none;
}

/* ===== Projects Grid ===== */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2.5rem;
  width: 100%;
  max-width: 1400px;
}

/* ===== Modern Project Card ===== */
.project-card {
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  border: 1px solid var(--bg-tertiary);
}

.project-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

/* ===== Image Wrapper ===== */
.img-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
  height: 220px;
  background: var(--bg-tertiary);
}

.img-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-slow);
}

.project-card:hover .img-wrapper img {
  transform: scale(1.08);
}

/* ===== Modern Overlay ===== */
.overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(0deg, rgba(102, 126, 234, 0.9) 0%, transparent 100%);
  color: white;
  padding: 1.5rem;
  opacity: 0;
  transition: var(--transition-normal);
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
  text-align: center;
  backdrop-filter: blur(8px);
}

.project-card:hover .overlay {
  opacity: 1;
}

/* ===== Project Title ===== */
.overlay h3 {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
  font-weight: 700;
  letter-spacing: -0.01em;
}

/* ===== Project Description ===== */
.overlay p {
  font-size: 1rem;
  margin-bottom: 1rem;
  line-height: 1.5;
  opacity: 0.95;
}

/* ===== Details Button ===== */
.btn-details {
  align-self: center;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: var(--radius-xl);
  border: none;
  background: white;
  color: var(--primary-color);
  box-shadow: var(--shadow-md);
  cursor: pointer;
  transition: var(--transition-fast);
  user-select: none;
}

.btn-details:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-1px);
}

/* ===== Additional Responsive Styles ===== */
@media (max-width: 600px) {
  .page.projects {
    padding: 3rem 1rem;
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .img-wrapper {
    height: 200px;
  }

  .overlay h3 {
    font-size: 1.25rem;
  }

  .overlay p {
    font-size: 0.9rem;
  }

  .btn-details {
    padding: 0.6rem 1.25rem;
    font-size: 0.9rem;
  }
}

/* ===== Utility Classes ===== */
.text-gradient {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.hover-lift {
  transition: var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

/* ===== Animations ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.pulse {
  animation: pulse 2s infinite;
}


