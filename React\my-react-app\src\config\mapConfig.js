// إعدادات الخريطة - يمكنك تعديل هذه القيم لتغيير موقع الخريطة

export const MAP_LOCATIONS = {
  // الموقع الافتراضي (العراق - بغداد)
  default: {
    latitude: 33.3152,
    longitude: 44.3661,
    zoom: 12,
    address: "بغداد، العراق",
    addressEn: "Baghdad, Iraq",
    placeName: "SmartTech AI",
    placeNameAr: "سمارت تك للذكاء الاصطناعي"
  },

  // مواقع أخرى يمكنك الاختيار منها
  locations: {
    // بغداد - منطقة الكرادة
    karada: {
      latitude: 33.3073,
      longitude: 44.4205,
      zoom: 15,
      address: "الكرادة، بغداد، العراق",
      addressEn: "Al-Karada, Baghdad, Iraq",
      placeName: "SmartTech AI - Karada Office",
      placeNameAr: "سمارت تك - مكتب الكرادة"
    },

    // بغداد - منطقة الجادرية
    jadriya: {
      latitude: 33.2778,
      longitude: 44.3833,
      zoom: 15,
      address: "الجادرية، بغداد، العراق",
      addressEn: "Al-Jadriya, Baghdad, Iraq",
      placeName: "SmartTech AI - Jadriya Office",
      placeNameAr: "سمارت تك - مكتب الجادرية"
    },

    // أربيل
    erbil: {
      latitude: 36.1911,
      longitude: 44.0093,
      zoom: 12,
      address: "أربيل، العراق",
      addressEn: "Erbil, Iraq",
      placeName: "SmartTech AI - Erbil Branch",
      placeNameAr: "سمارت تك - فرع أربيل"
    },

    // البصرة
    basra: {
      latitude: 30.5085,
      longitude: 47.7804,
      zoom: 12,
      address: "البصرة، العراق",
      addressEn: "Basra, Iraq",
      placeName: "SmartTech AI - Basra Branch",
      placeNameAr: "سمارت تك - فرع البصرة"
    },

    // دبي (للتوسع المستقبلي)
    dubai: {
      latitude: 25.2048,
      longitude: 55.2708,
      zoom: 12,
      address: "دبي، الإمارات العربية المتحدة",
      addressEn: "Dubai, UAE",
      placeName: "SmartTech AI - Dubai Office",
      placeNameAr: "سمارت تك - مكتب دبي"
    },

    // الرياض (للتوسع المستقبلي)
    riyadh: {
      latitude: 24.7136,
      longitude: 46.6753,
      zoom: 12,
      address: "الرياض، المملكة العربية السعودية",
      addressEn: "Riyadh, Saudi Arabia",
      placeName: "SmartTech AI - Riyadh Office",
      placeNameAr: "سمارت تك - مكتب الرياض"
    }
  }
};

// الموقع المحدد حالياً - غير هذا لتغيير الموقع المعروض
export const CURRENT_LOCATION = MAP_LOCATIONS.default;

// دالة لتحديث الموقع الحالي
export const setCurrentLocation = (locationKey) => {
  if (MAP_LOCATIONS.locations[locationKey]) {
    Object.assign(CURRENT_LOCATION, MAP_LOCATIONS.locations[locationKey]);
    return true;
  }
  return false;
};

// دالة لإضافة موقع جديد
export const addNewLocation = (key, locationData) => {
  MAP_LOCATIONS.locations[key] = locationData;
};

// دالة للحصول على جميع المواقع المتاحة
export const getAllLocations = () => {
  return {
    default: MAP_LOCATIONS.default,
    ...MAP_LOCATIONS.locations
  };
};

// إعدادات إضافية للخريطة
export const MAP_SETTINGS = {
  defaultHeight: "400px",
  defaultZoom: 15,
  showLocationInfo: true,
  showOpenInMapsButton: true,
  enableFullscreen: true,
  
  // ألوان وتصميم
  borderRadius: "12px",
  
  // نصوص
  texts: {
    ar: {
      title: "موقعنا على الخريطة",
      loading: "جاري تحميل الخريطة...",
      error: "عذراً، لا يمكن تحميل الخريطة",
      openInMaps: "فتح في خرائط جوجل",
      selectLocation: "اختر الموقع"
    },
    en: {
      title: "Our Location",
      loading: "Loading map...",
      error: "Sorry, cannot load the map",
      openInMaps: "Open in Google Maps",
      selectLocation: "Select Location"
    }
  }
};

// تعليمات لتخصيص الخريطة:
/*
لتغيير موقع الخريطة:

1. الطريقة السهلة:
   - غير قيمة CURRENT_LOCATION إلى أحد المواقع المحددة مسبقاً
   - مثال: export const CURRENT_LOCATION = MAP_LOCATIONS.locations.karada;

2. إضافة موقع جديد:
   - أضف موقعاً جديداً في MAP_LOCATIONS.locations
   - استخدم Google Maps للحصول على الإحداثيات الصحيحة

3. تخصيص الموقع الافتراضي:
   - عدل قيم MAP_LOCATIONS.default مباشرة

للحصول على إحداثيات موقع معين:
1. اذهب إلى Google Maps
2. انقر بالزر الأيمن على الموقع المطلوب
3. اختر "What's here?" أو "ما هذا المكان؟"
4. ستظهر الإحداثيات في أسفل الشاشة

مثال للإحداثيات:
- خط العرض (Latitude): 33.3152
- خط الطول (Longitude): 44.3661
*/
