/* ===== Modern Home Page ===== */
.page.home {
  font-family: var(--font-primary);
  color: var(--text-primary);
  line-height: 1.6;
  background: var(--bg-secondary);
}

/* ===== Top Alert Banner ===== */
.top-alert {
  background: var(--accent-gradient);
  color: white;
  text-align: center;
  padding: 1rem;
  font-weight: 600;
  font-size: 0.95rem;
  position: relative;
  overflow: hidden;
  animation: slideInDown 0.5s ease-out;
}

.top-alert::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  animation: shimmer 3s infinite;
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* ===== Modern Hero Section ===== */
.hero-section {
  position: relative;
  color: white;
  text-align: center;
  overflow: hidden;
  border-radius: var(--radius-2xl);
  margin: 2rem;
  box-shadow: var(--shadow-xl);
}

.hero-section img {
  width: 100%;
  height: auto;
  min-height: 500px;
  object-fit: cover;
  filter: brightness(0.4) contrast(1.1);
}

.hero-section > div {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  z-index: 2;
}

.hero-section h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  margin-bottom: 1.5rem;
  font-weight: 800;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  animation: fadeInUp 1s ease-out;
}

.hero-section p {
  font-size: clamp(1.1rem, 2.5vw, 1.4rem);
  margin-bottom: 2rem;
  opacity: 0.95;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
  animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-section .cta-button {
  background: var(--accent-gradient);
  color: white;
  padding: 1rem 2.5rem;
  border-radius: var(--radius-xl);
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  transition: var(--transition-normal);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 1s ease-out 0.4s both;
  position: relative;
  overflow: hidden;
}

.hero-section .cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

.hero-section .cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: var(--transition-slow);
}

.hero-section .cta-button:hover::before {
  left: 100%;
}

/* ===== Values Section ===== */
.values-section {
  padding: 5rem 2rem;
  background: var(--bg-primary);
  text-align: center;
  margin: 2rem;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--bg-tertiary);
}

.values-section h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 3rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.values-section ul {
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.values-section li {
  background: var(--bg-secondary);
  padding: 2rem;
  border-radius: var(--radius-xl);
  font-size: 1.1rem;
  font-weight: 500;
  transition: var(--transition-normal);
  border: 1px solid var(--bg-tertiary);
  position: relative;
  overflow: hidden;
}

.values-section li::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.values-section li:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

/* ===== About Preview ===== */
.about-preview {
  padding: 5rem 2rem;
  text-align: center;
  background: var(--bg-secondary);
  margin: 2rem;
  border-radius: var(--radius-2xl);
}

.about-preview h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 2rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.about-preview p {
  font-size: 1.2rem;
  line-height: 1.8;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto 2rem;
}

.link-button {
  margin-top: 1.5rem;
  display: inline-block;
  background: var(--primary-gradient);
  color: white;
  padding: 1rem 2rem;
  border-radius: var(--radius-xl);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-md);
}

.link-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* ===== Services Preview ===== */
.services-preview {
  padding: 5rem 2rem;
  background: var(--bg-primary);
  text-align: center;
  margin: 2rem;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--bg-tertiary);
}

.services-preview h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 3rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.service-slider .card {
  background: var(--bg-secondary);
  padding: 2.5rem 2rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  margin: 1rem;
  text-align: center;
  transition: var(--transition-normal);
  border: 1px solid var(--bg-tertiary);
  position: relative;
  overflow: hidden;
}

.service-slider .card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.service-slider .card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.service-slider .card svg {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
}

.service-slider .card h3 {
  margin-top: 1rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.3rem;
}

.service-slider .card p {
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* ===== Workflow Section ===== */
.workflow-section {
  padding: 5rem 2rem;
  background: var(--bg-secondary);
  text-align: center;
  margin: 2rem;
  border-radius: var(--radius-2xl);
}

.workflow-section h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 3rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.workflow-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.step {
  background: var(--bg-primary);
  padding: 2.5rem 2rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  text-align: center;
  transition: var(--transition-normal);
  border: 1px solid var(--bg-tertiary);
  position: relative;
  overflow: hidden;
}

.step::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.step:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.step svg {
  color: var(--primary-color);
  font-size: 3rem;
  margin-bottom: 1.5rem;
  filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
}

.step h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-weight: 600;
}

.step p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* ===== Blog Section ===== */
.blog-section {
  padding: 5rem 2rem;
  text-align: center;
  background: var(--bg-secondary);
  margin: 2rem;
  border-radius: var(--radius-2xl);
}

.blog-section h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 3rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.blog-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.blog-post {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  border: 1px solid var(--bg-tertiary);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.blog-post::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.blog-post:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.blog-post h3 {
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.blog-post h3 svg {
  color: var(--primary-color);
}

.blog-post p {
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

.blog-post .read-more {
  color: var(--primary-color);
  font-weight: 600;
  margin-left: auto;
}

/* ===== Contact CTA ===== */
.contact-cta {
  padding: 5rem 2rem;
  background: var(--primary-gradient);
  color: white;
  text-align: center;
  margin: 2rem;
  border-radius: var(--radius-2xl);
  position: relative;
  overflow: hidden;
}

.contact-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  pointer-events: none;
}

.contact-cta h3 {
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  margin-bottom: 2rem;
  font-weight: 700;
  position: relative;
  z-index: 1;
}

.contact-cta .cta-button {
  background: white;
  color: var(--primary-color);
  position: relative;
  z-index: 1;
}

/* ===== Fixed Consultation Button ===== */
.floating-cta {
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  background: var(--accent-gradient);
  color: white;
  padding: 0.75rem 1.25rem;
  border-radius: var(--radius-lg);
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: var(--shadow-xl);
  z-index: 1000;
  transition: var(--transition-normal);
  opacity: 1;
  visibility: visible;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
  border: 2px solid transparent;
}

.floating-cta:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-elegant);
  background: var(--primary-gradient);
  border-color: var(--accent-color);
}

.floating-cta .cta-icon {
  font-size: 1rem;
  animation: ring 2s infinite;
}

.floating-cta .cta-text {
  font-weight: 600;
  letter-spacing: 0.025em;
}

/* Ring animation for phone icon */
@keyframes ring {
  0%, 100% { transform: rotate(0deg); }
  10%, 30% { transform: rotate(-10deg); }
  20% { transform: rotate(10deg); }
}

/* ===== Testimonials Section ===== */
.testimonials-section {
  padding: 5rem 2rem;
  background: var(--bg-primary);
  text-align: center;
  margin: 2rem;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--bg-tertiary);
}

.testimonials-section h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 3rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.testimonials {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.testimonial {
  background: var(--bg-secondary);
  padding: 2.5rem 2rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  border: 1px solid var(--bg-tertiary);
  position: relative;
  overflow: hidden;
}

.testimonial::before {
  content: '"';
  position: absolute;
  top: 1rem;
  left: 1.5rem;
  font-size: 4rem;
  color: var(--primary-color);
  opacity: 0.3;
  font-family: serif;
}

.testimonial:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.testimonial p {
  font-style: italic;
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 1;
}

.testimonial strong {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
}

/* ===== FAQ Section ===== */
.faq-section {
  padding: 5rem 2rem;
  background: var(--bg-secondary);
  text-align: center;
  margin: 2rem;
  border-radius: var(--radius-2xl);
}

.faq-section h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 3rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.faq-section details {
  margin: 1rem auto;
  max-width: 800px;
  background: var(--bg-primary);
  padding: 1.5rem 2rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  cursor: pointer;
  border: 1px solid var(--bg-tertiary);
  transition: var(--transition-fast);
}

.faq-section details:hover {
  box-shadow: var(--shadow-lg);
}

.faq-section details[open] {
  background: var(--bg-primary);
  box-shadow: var(--shadow-lg);
}

.faq-section summary {
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--text-primary);
  padding: 0.5rem 0;
  list-style: none;
  position: relative;
}

.faq-section summary::after {
  content: '+';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.5rem;
  color: var(--primary-color);
  transition: var(--transition-fast);
}

.faq-section details[open] summary::after {
  content: '−';
  transform: translateY(-50%) rotate(180deg);
}

.faq-section details p {
  margin-top: 1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  text-align: left;
}

/* ===== Elegant Map Section ===== */
.map-section {
  padding: 5rem 2rem;
  background: var(--bg-primary);
  text-align: center;
  margin: 2rem;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-elegant);
  border: 1px solid var(--bg-tertiary);
}

.map-section h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 3rem;
  color: var(--primary-color);
  font-weight: 700;
}

.map-container {
  max-width: 900px;
  margin: 0 auto;
}

.map-wrapper {
  position: relative;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-elegant);
  margin-bottom: 2rem;
}

.map-wrapper iframe {
  width: 100%;
  height: 400px;
  border: none;
}

/* سهم أحمر للموقع */
.location-pointer {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  pointer-events: none;
}

.red-arrow {
  position: relative;
  animation: bounce 2s infinite;
}

.red-arrow svg {
  filter: drop-shadow(0 4px 8px rgba(220, 38, 38, 0.4));
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border: 3px solid #dc2626;
  border-radius: 50%;
  opacity: 0;
  animation: pulse 2s infinite;
}

.location-label {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: #dc2626;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.location-label::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: #dc2626;
}

/* معلومات الموقع */
.location-info {
  background: var(--bg-accent);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--bg-tertiary);
}

.location-details h4 {
  color: var(--primary-color);
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.location-details p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

.directions-btn {
  background: var(--accent-gradient);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-md);
}

.directions-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: var(--primary-gradient);
}

/* الحركات */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes pulse {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.5);
  }
}

/* Dark mode adjustments for Home page */
[data-theme="dark"] .location-info {
  background: var(--bg-tertiary);
  border-color: var(--bg-tertiary);
}

[data-theme="dark"] .location-details h4 {
  color: var(--text-primary);
}

[data-theme="dark"] .location-details p {
  color: var(--text-secondary);
}

/* Dark mode for hero section */
[data-theme="dark"] .hero-section {
  background: var(--hero-gradient);
  color: var(--text-primary);
}

[data-theme="dark"] .hero-content h1 {
  background: var(--luxury-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

[data-theme="dark"] .hero-content p {
  color: var(--text-secondary);
}

/* Dark mode for sections */
[data-theme="dark"] .values-section,
[data-theme="dark"] .about-preview,
[data-theme="dark"] .services-preview,
[data-theme="dark"] .contact-cta,
[data-theme="dark"] .testimonials-section,
[data-theme="dark"] .faq-section,
[data-theme="dark"] .case-studies-section,
[data-theme="dark"] .blog-section,
[data-theme="dark"] .robots-gallery {
  background: var(--bg-primary);
  color: var(--text-primary);
}

/* Dark mode for cards */
[data-theme="dark"] .card,
[data-theme="dark"] .robot-card,
[data-theme="dark"] .blog-post,
[data-theme="dark"] .case-study-card,
[data-theme="dark"] .testimonial {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--bg-tertiary);
}

/* Dark mode for slick slider */
[data-theme="dark"] .slick-dots li button:before {
  color: var(--accent-color);
}

[data-theme="dark"] .slick-dots li.slick-active button:before {
  color: var(--accent-light);
}

/* Dark mode for FAQ */
[data-theme="dark"] details {
  background: var(--bg-tertiary);
  border-color: var(--bg-tertiary);
}

[data-theme="dark"] summary {
  color: var(--text-primary);
}

[data-theme="dark"] details[open] summary {
  color: var(--accent-color);
}

/* Dark mode for floating CTA */
[data-theme="dark"] .floating-cta {
  background: var(--accent-gradient);
  color: white;
  box-shadow: var(--shadow-elegant);
  border-color: transparent;
}

[data-theme="dark"] .floating-cta:hover {
  background: var(--primary-gradient);
  border-color: var(--accent-color);
  box-shadow: var(--shadow-luxury);
}

/* Responsive adjustments for map */
@media (max-width: 768px) {
  .map-wrapper iframe {
    height: 300px;
  }

  .location-pointer {
    transform: translate(-50%, -60%);
  }

  .red-arrow svg {
    width: 30px;
    height: 30px;
  }

  .pulse-ring {
    width: 45px;
    height: 45px;
  }

  .location-label {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    top: -35px;
  }

  .location-info {
    padding: 1.5rem;
  }

  .location-details h4 {
    font-size: 1.25rem;
  }

  .directions-btn {
    width: 100%;
    padding: 1rem;
  }
}
/* ===== Case Studies Section ===== */
.case-studies-section {
  padding: 5rem 2rem;
  background: var(--bg-primary);
  text-align: center;
  margin: 2rem;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--bg-tertiary);
}

.case-studies-section h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 3rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.case-studies-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.case-study-card {
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  padding: 2rem;
  text-align: left;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  border: 1px solid var(--bg-tertiary);
  position: relative;
  overflow: hidden;
}

.case-study-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--primary-gradient);
}

.case-study-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.icon-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 1rem 0 0.5rem;
}

.icon-title h3 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--text-primary);
  font-weight: 600;
}

.icon-title .icon {
  font-size: 1.5rem;
}

.case-study-card p {
  margin-bottom: 1rem;
  line-height: 1.6;
  color: var(--text-secondary);
}

/* ===== Robots Gallery ===== */
.robots-gallery {
  padding: 5rem 2rem;
  text-align: center;
  background: var(--bg-secondary);
  margin: 2rem;
  border-radius: var(--radius-2xl);
}

.robots-gallery h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 3rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.robots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.robot-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  border: 1px solid var(--bg-tertiary);
  cursor: pointer;
  position: relative;
}

.robot-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.robot-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.robot-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: var(--transition-slow);
}

.robot-card:hover img {
  transform: scale(1.05);
}

.robot-card h3 {
  font-size: 1.3rem;
  color: var(--text-primary);
  font-weight: 600;
  margin: 1.5rem 0 1rem;
  padding: 0 1.5rem;
}

.robot-card p {
  font-size: 1rem;
  color: var(--text-secondary);
  padding: 0 1.5rem 1.5rem;
  line-height: 1.6;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
  .page.home {
    margin: 1rem;
  }

  .hero-section,
  .values-section,
  .about-preview,
  .services-preview,
  .workflow-section,
  .blog-section,
  .testimonials-section,
  .faq-section,
  .map-section,
  .case-studies-section,
  .robots-gallery,
  .contact-cta {
    margin: 1rem;
    padding: 3rem 1.5rem;
  }

  .workflow-steps,
  .values-section ul,
  .testimonials,
  .case-studies-list,
  .robots-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .blog-list {
    grid-template-columns: 1fr;
  }

  .floating-cta {
    bottom: 1rem;
    right: 1rem;
    padding: 0.6rem 1rem;
    font-size: 0.8rem;
    border-radius: var(--radius-md);
  }
}

@media (max-width: 480px) {
  .hero-section,
  .values-section,
  .about-preview,
  .services-preview,
  .workflow-section,
  .blog-section,
  .testimonials-section,
  .faq-section,
  .map-section,
  .case-studies-section,
  .robots-gallery,
  .contact-cta {
    margin: 0.5rem;
    padding: 2rem 1rem;
  }

  .top-alert {
    font-size: 0.85rem;
    padding: 0.75rem;
  }

  .service-slider .card {
    margin: 0.5rem;
    padding: 1.5rem 1rem;
  }

  .case-study-card {
    text-align: center;
  }

  .icon-title {
    justify-content: center;
  }
}


