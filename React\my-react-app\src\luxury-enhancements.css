/* ===== Elegant Enhancements ===== */

/* Animated gradient backgrounds */
@keyframes elegantGradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.elegant-animated-bg {
  background: linear-gradient(-45deg, #1a365d, #2d5a87, #c6a96b, #d4b876);
  background-size: 400% 400%;
  animation: elegantGradient 15s ease infinite;
}

/* Floating particles effect */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.floating-element {
  animation: float 6s ease-in-out infinite;
}

/* Luxury glow effects */
.luxury-glow {
  position: relative;
}

.luxury-glow::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--luxury-gradient);
  border-radius: inherit;
  z-index: -1;
  filter: blur(8px);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.luxury-glow:hover::before {
  opacity: 0.7;
}

/* Premium card styles */
.premium-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(212, 175, 55, 0.3);
  border-radius: var(--radius-xl);
  box-shadow: 
    0 8px 32px rgba(212, 175, 55, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.premium-card:hover {
  transform: translateY(-5px);
  box-shadow: 
    0 20px 40px rgba(212, 175, 55, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Luxury text effects */
.luxury-text {
  background: var(--luxury-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(212, 175, 55, 0.3);
}

/* Premium buttons */
.premium-button {
  background: var(--luxury-gradient);
  border: none;
  color: white;
  padding: 1rem 2rem;
  border-radius: var(--radius-xl);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-luxury);
}

.premium-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.premium-button:hover::before {
  left: 100%;
}

.premium-button:hover {
  transform: translateY(-3px);
  box-shadow: 
    var(--shadow-luxury),
    0 15px 35px rgba(212, 175, 55, 0.5);
}

/* Luxury scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: var(--luxury-gradient);
  border-radius: 6px;
  border: 2px solid var(--bg-secondary);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-gradient);
}

/* Luxury loading animation */
@keyframes luxuryPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.05); }
}

.luxury-pulse {
  animation: luxuryPulse 2s ease-in-out infinite;
}

/* Premium section dividers */
.luxury-divider {
  height: 2px;
  background: var(--luxury-gradient);
  margin: 3rem 0;
  border-radius: 1px;
  position: relative;
}

.luxury-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: var(--luxury-gradient);
  border-radius: 50%;
  box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
}

/* Luxury hover effects for images */
.luxury-image {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-xl);
  transition: all 0.3s ease;
}

.luxury-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--luxury-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
  mix-blend-mode: overlay;
}

.luxury-image:hover::before {
  opacity: 0.3;
}

.luxury-image:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-luxury);
}

/* Premium form inputs */
.luxury-input {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(212, 175, 55, 0.3);
  border-radius: var(--radius-lg);
  padding: 1rem;
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.luxury-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 
    0 0 0 3px rgba(212, 175, 55, 0.2),
    var(--shadow-luxury);
  background: rgba(255, 255, 255, 0.15);
}

/* Dark mode adjustments for luxury elements */
[data-theme="dark"] .premium-card {
  background: rgba(15, 23, 42, 0.8);
  border-color: rgba(212, 175, 55, 0.4);
}

[data-theme="dark"] .luxury-input {
  background: rgba(15, 23, 42, 0.6);
  border-color: rgba(212, 175, 55, 0.4);
  color: var(--text-primary);
}

[data-theme="dark"] .luxury-input:focus {
  background: rgba(15, 23, 42, 0.8);
  border-color: var(--primary-color);
}

/* Responsive luxury adjustments */
@media (max-width: 768px) {
  .luxury-glow::before {
    filter: blur(4px);
  }
  
  .premium-button {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
  
  .luxury-divider::before {
    width: 15px;
    height: 15px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .luxury-animated-bg,
  .floating-element,
  .luxury-pulse {
    animation: none;
  }
  
  .premium-button::before {
    transition: none;
  }
}
