// src/components/About.js
import React, { useContext } from "react";
import { LanguageContext } from "../context/LanguageContext";
import "./About.css";

const employees = [
  {
    id: 1,
    name: { ar: "عباس فاضل", en: "<PERSON>ade<PERSON>" },
    role: { ar: "مدير التطوير", en: "Development Manager" },
    photo: "/images/5280739875177489190_121.jpg",
    bio: {
      ar: "خبير في تطوير البرمجيات مع أكثر من 10 سنوات خبرة.",
      en: "Software expert with over 10 years of experience.",
    },
  },
  {
    id: 2,
    name: { ar: "اسراء كامل", en: "<PERSON><PERSON><PERSON><PERSON>" },
    role: { ar: "مهندسة ذكاء اصطناعي", en: "AI Engineer" },
    photo: "/images/istockphoto-613532476-612x612.jpg",
    bio: {
      ar: "متخصصة في الذكاء الاصطناعي والتعلم الآلي.",
      en: "Specialist in AI and machine learning.",
    },
  },
  {
    id: 3,
    name: { ar: "خالد علي", en: "Khaled Ali" },
    role: { ar: "مصمم واجهات المستخدم", en: "UI/UX Designer" },
    photo: "/images/istockphoto-534506959-612x612.jpg",
    bio: {
      ar: "مبدع في تصميم تجارب المستخدم المتميزة.",
      en: "Creative in designing outstanding user experiences.",
    },
  },
];

export default function About() {
  const { lang, toggleLang } = useContext(LanguageContext);

  return (
    <section className="page about" dir={lang === "ar" ? "rtl" : "ltr"}>
      <div style={{ textAlign: lang === "ar" ? "left" : "right", marginBottom: 20 }}>
        <button
          onClick={toggleLang}
          style={{
            padding: "8px 16px",
            cursor: "pointer",
            borderRadius: 5,
            border: "1px solid #3f51b5",
            backgroundColor: "#3f51b5",
            color: "#fff",
          }}
          aria-label={lang === "ar" ? "تبديل إلى الإنجليزية" : "Switch to Arabic"}
        >
          {lang === "ar" ? "English" : "العربية"}
        </button>
      </div>

      <h1>{lang === "ar" ? "من نحن" : "About Us"}</h1>
      <p style={{ maxWidth: 600, margin: "10px auto 40px auto", fontSize: "1.15rem", color: "#444" }}>
        {lang === "ar"
          ? "شركة سمارت تك تأسست في 2020، متخصصة في تطوير حلول برمجية متقدمة وتقنيات الذكاء الاصطناعي."
          : "SmartTech was founded in 2020, specializing in advanced software solutions and AI technologies."}
      </p>

      <h2 style={{ marginBottom: 30 }}>{lang === "ar" ? "فريق العمل" : "Our Team"}</h2>

      <div
        className="employees-container"
        style={{ display: "flex", justifyContent: "center", gap: 25, flexWrap: "wrap" }}
      >
        {employees.map(({ id, name, role, photo, bio }) => (
          <div
            key={id}
            className="employee-card"
            style={{
              background: "#fff",
              borderRadius: 15,
              boxShadow: "0 8px 30px rgba(0,0,0,0.12)",
              padding: 20,
              width: 280,
              cursor: "default",
              userSelect: "none",
              textAlign: lang === "ar" ? "right" : "left",
              color: "#222",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
            }}
            tabIndex={0}
            aria-label={`${name[lang]} - ${role[lang]}`}
          >
            <img
              src={photo}
              alt={name[lang]}
              className="employee-photo"
              style={{
                width: 120,
                height: 120,
                borderRadius: "50%",
                objectFit: "cover",
                marginBottom: 15,
                border: "3px solid #3f51b5",
              }}
            />
            <h3 style={{ margin: "10px 0 5px 0", fontWeight: "700" }}>{name[lang]}</h3>
            <h4 style={{ margin: "0 0 15px 0", color: "#3f51b5", fontWeight: "600" }}>{role[lang]}</h4>
            <p style={{ fontSize: "1rem", color: "#555", minHeight: 72 }}>{bio[lang]}</p>
          </div>
        ))}
      </div>
    </section>
  );
}
