import React, { useContext, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { LanguageContext } from '../context/LanguageContext';
import Map from '../components/Map';
import AOS from 'aos';
import 'aos/dist/aos.css';
import './Home.css';

import Slider from "react-slick";
import {
  FaLaptopCode,
  FaRobot,
  FaTools,
  FaComments,
  FaPencilRuler,
  FaCheckCircle,
  FaBookOpen,
  FaArrowRight
} from 'react-icons/fa';

import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

export default function Home() {
  const { lang } = useContext(LanguageContext);
  const [openArticle, setOpenArticle] = useState(null);

  useEffect(() => {
    AOS.init({ duration: 1000 });
  }, []);

  const t = {
    ar: {
      heroTitle: "نحو مستقبل رقمي أذكى",
      heroText: "نساعدك على التحول الرقمي من خلال حلول برمجية وذكاء اصطناعي مخصصة.",
      discover: "استكشف خدماتنا",
      valuesTitle: "لماذا تختار SmartTech AI؟",
      values: [
        "حلول مخصصة حسب احتياجاتك",
        "خبراء في الذكاء الاصطناعي",
        "دعم فني متواصل 24/7"
      ],
      aboutTitle: "من نحن",
      aboutText: "نحن فريق من المطورين وخبراء الذكاء الاصطناعي نعمل بشغف لبناء مستقبل رقمي أذكى للمؤسسات العربية.",
      previewTitle: "بعض خدماتنا",
      contactCta: "هل لديك فكرة أو مشروع؟ لنتحدث!",
      testimonialsTitle: "ماذا يقول عملاؤنا؟",
      faqTitle: "الأسئلة الشائعة",
      mapTitle: "موقعنا على الخريطة",
      blogTitle: "مقالات المعرفة",
      blogArticles: [
        {
          id: 1,
          title: "أهمية الذكاء الاصطناعي في التحول الرقمي",
          summary: "كيف يؤثر الذكاء الاصطناعي على تطوير الأعمال الرقمية.",
          content: "الذكاء الاصطناعي أصبح عنصرًا رئيسيًا في تسريع عمليات التحول الرقمي، حيث يساعد في تحليل البيانات واتخاذ قرارات دقيقة..."
        },
        {
          id: 2,
          title: "أفضل الممارسات في تطوير الويب الحديث",
          summary: "نصائح وتقنيات لتحسين أداء وتصميم المواقع الإلكترونية.",
          content: "لتطوير ويب ناجح، يجب التركيز على الأداء، واجهة المستخدم، وتجربة المستخدم، بالإضافة إلى أمان الموقع..."
        },
        {
          id: 3,
          title: "كيف تختار الحلول التقنية المناسبة لشركتك",
          summary: "دليل لاختيار أفضل الأدوات البرمجية والتقنية.",
          content: "اختيار الحلول التقنية يعتمد على حجم المشروع، الأهداف، وميزانية الشركة. يجب دراسة الخيارات المتاحة بعناية..."
        },
      ],
      caseTitle: "قصص نجاح",
      caseStudies: [
        {
          challenge: "أتمتة شركة توصيل تواجه تحديات في التتبع اليدوي.",
          solution: "نظام تتبع ذكي باستخدام الذكاء الاصطناعي.",
          result: "تحسّن الأداء بنسبة 40٪ وسرعة التسليم."
        },
        {
          challenge: "بطء موقع التجارة الإلكترونية.",
          solution: "إعادة بناء الموقع باستخدام React وHeadless CMS.",
          result: "زيادة سرعة التصفح بنسبة 60٪ وزيادة المبيعات."
        }
      ],
    },
    en: {
      heroTitle: "Toward a Smarter Digital Future",
      heroText: "We help you digitize your business through custom AI and software solutions.",
      discover: "Discover Our Services",
      valuesTitle: "Why Choose SmartTech AI?",
      values: [
        "Custom solutions tailored to your needs",
        "AI & ML experts",
        "24/7 Technical Support"
      ],
      aboutTitle: "About Us",
      aboutText: "We are a team of developers and AI experts passionate about building a smarter digital future.",
      previewTitle: "Some of Our Services",
      contactCta: "Got an idea or project? Let’s talk!",
      testimonialsTitle: "What Our Clients Say",
      faqTitle: "Frequently Asked Questions",
      mapTitle: "Our Location",
      blogTitle: "Knowledge Articles",
      blogArticles: [
        {
          id: 1,
          title: "The Role of AI in Digital Transformation",
          summary: "How AI influences the growth of digital businesses.",
          content: "Artificial Intelligence has become a key player in accelerating digital transformation processes by helping analyze data and making accurate decisions..."
        },
        {
          id: 2,
          title: "Best Practices in Modern Web Development",
          summary: "Tips and techniques to improve website performance and design.",
          content: "Successful web development requires focusing on performance, UI, UX, and website security..."
        },
        {
          id: 3,
          title: "How to Choose the Right Tech Solutions for Your Business",
          summary: "A guide to selecting the best software and tech tools.",
          content: "Choosing tech solutions depends on project size, goals, and budget. Careful evaluation of available options is essential..."
        },
      ],
      caseTitle: "Case Studies",
      caseStudies: [
        {
          challenge: "Delivery company struggling with manual tracking.",
          solution: "Smart AI-powered tracking system.",
          result: "Performance improved by 40% and faster deliveries."
        },
        {
          challenge: "Slow e-commerce website.",
          solution: "Rebuilt using React and a headless CMS.",
          result: "Browsing speed improved by 60%, boosting sales."
        }
      ],
    }
  };

  const text = t[lang];

  const sliderSettings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    responsive: [
      { breakpoint: 1024, settings: { slidesToShow: 2 } },
      { breakpoint: 600, settings: { slidesToShow: 1 } }
    ]
  };

   
const robots = [
  {
    id: 1,
    img: '/img/robot1.jpg',
    title:'روبوت سياره الي',
    description: 'روبوت يعمل كمركبة ذكية للتنقل الذاتي.'
  },
  {
    id: 2,
    img: '/img/robot2.jpg',
    title: 'روبوت طبي مساعد',
    description:'يُستخدم لمساعدة الأطباء في الجراحة والعلاج.'
  },
  {
    id: 3,
    img: '/img/robot3.jpg',
    title: 'روبوت تعليمي',
    description: 'يُستخدم في تعليم البرمجة والذكاء الاصطناعي للأطفال.'
  },
  {
    id: 4,
    img: '/img/robot4.jpg',
    title: 'روبوت أمني',
    description: 'مخصص للمراقبة والحماية باستخدام تقنيات التعرف على الوجه.'
  },
  {
    id: 5,
    img: '/img/robot5.jpg',
    title: 'روبوت خدمة العملاء',
    description: 'يتفاعل مع الزبائن في الأماكن العامة ويوفر معلومات وخدمات.'
  },
  {
    id: 6,
    img: '/img/robot6.jpg',
    title: 'روبوت زراعي',
    description: 'روبوت مخصص للأعمال الزراعية مثل الري، الحصاد، ومراقبة التربة باستخدام الذكاء الاصطناعي.'
  },
   {
    id: 8,
    img: '/img/robot7.jpg',
    title: 'روبوت صناعي',
    description: 'يُستخدم في خطوط الإنتاج للتجميع واللحام بجودة فائقة.'
  },
   {
  id: 9,
  img: '/img/robot8.jpg',
  title: 'روبوت برمجي',
  description: 'روبوت مخصص لتعليم البرمجة وتحليل الشيفرات باستخدام تقنيات الذكاء الاصطناعي.'
 }
];


  return (
    <>
      {/* ✅ TOP ALERT */}
      <div className="top-alert">
        {lang === 'ar' ? '🎉 خصم 20% على باقات الذكاء الاصطناعي – لفترة محدودة!' : '🎉 20% OFF AI Packages – Limited Time!'}
      </div>

      <div className="page home">

        {/* ✅ HERO */}
        <section className="hero-section" data-aos="fade-up" style={{ position: 'relative', textAlign: 'center', color: 'white' }}>
  <img
    src="/images/h3.jpg"  // أو src={heroImage} لو مستورد
    alt="Hero Image"
    style={{ width: '100%', height: 'auto', filter: 'brightness(50%)' }} // تخفيف إضاءة الصورة عشان النص يبرز
  />
  <div style={{
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '100%',
    padding: '0 1rem',
    
  }}>
    <h1 style={{ fontSize: '2.5rem', fontWeight: 'bold' }}>{text.heroTitle}</h1>
    <p style={{ fontSize: '1.2rem', marginTop: '0.5rem' }}>{text.heroText}</p>
    <Link to="/services" className="cta-button">{text.discover}</Link>
  </div>
</section>


        {/* ✅ VALUES */}
        <section className="values-section" data-aos="fade-up">
          <h2>{text.valuesTitle}</h2>
          <ul>
            {text.values.map((val, i) => (
              <li key={i}>✅ {val}</li>
            ))}
          </ul>
        </section>

        {/* ✅ ABOUT */}
        <section className="about-preview" data-aos="fade-right">
          <h2>{text.aboutTitle}</h2>
          <p>{text.aboutText}</p>
          <Link to="/about" className="link-button">{lang === 'ar' ? "اعرف المزيد" : "Learn more"}</Link>
        </section>

        {/* ✅ SERVICES SLIDER */}
        <section className="services-preview" data-aos="zoom-in">
          <h2>{text.previewTitle}</h2>
          <Slider {...sliderSettings} className="service-slider">
            <div className="card">
              <FaLaptopCode size={40} />
              <h3>{lang === "ar" ? "تطوير مواقع" : "Web Development"}</h3>
              <p>{lang === "ar" ? "مواقع عصرية وسريعة." : "Modern and fast websites."}</p>
            </div>
            <div className="card">
              <FaRobot size={40} />
              <h3>{lang === "ar" ? "ذكاء اصطناعي" : "AI Solutions"}</h3>
              <p>{lang === "ar" ? "حلول ذكية للأعمال." : "Smart business solutions."}</p>
            </div>
            <div className="card">
              <FaTools size={40} />
              <h3>{lang === "ar" ? "صيانة ودعم" : "Maintenance & Support"}</h3>
              <p>{lang === "ar" ? "دعم تقني مستمر." : "Ongoing technical support."}</p>
            </div>
          </Slider>
        </section>


       <section className="robots-gallery" style={{ padding: '2rem', textAlign: 'center' }}>
      <h2 data-aos="fade-up">مجموعة الروبوتات والذكاء الاصطناعي</h2>
      <div
        className="robots-grid"
        style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1.5rem',
          marginTop: '1.5rem',
        }}
      >
        {robots.map((robot) => (
          <div
            key={robot.id}
            className="robot-card"
            data-aos="zoom-in"
            style={{
              border: '1px solid #ddd',
              borderRadius: '8px',
              padding: '1rem',
              boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
              cursor: 'pointer',
              transition: 'transform 0.3s ease',
            }}
            onClick={() => window.open(robot.img, '_blank')} // تفتح الصورة في تاب جديد عند النقر (اختياري)
            onMouseEnter={(e) => (e.currentTarget.style.transform = 'scale(1.05)')}
            onMouseLeave={(e) => (e.currentTarget.style.transform = 'scale(1)')}
          >
            <img
              src={robot.img}
              alt={robot.title}
              style={{ width: '100%', borderRadius: '6px', marginBottom: '0.8rem' }}
              loading="lazy"
            />
            <h3>{robot.title}</h3>
            <p style={{ fontSize: '0.9rem', color: '#555' }}>{robot.description}</p>
          </div>
        ))}
      </div>
    </section>
  
 




        {/* ✅ WORKFLOW */}
        <section className="workflow-section" data-aos="fade-up">
          <h2>{lang === 'ar' ? 'كيف نعمل؟' : 'How We Work'}</h2>
          <div className="workflow-steps">
            <div className="step"><FaComments size={24} /><h3>{lang === 'ar' ? 'التحليل' : 'Analyze'}</h3></div>
            <div className="step"><FaPencilRuler size={24} /><h3>{lang === 'ar' ? 'التصميم' : 'Design'}</h3></div>
            <div className="step"><FaLaptopCode size={24} /><h3>{lang === 'ar' ? 'البرمجة' : 'Develop'}</h3></div>
            <div className="step"><FaCheckCircle size={24} /><h3>{lang === 'ar' ? 'الاختبار' : 'Test'}</h3></div>
          </div>
        </section>

        {/* ✅ CASE STUDIES */}
        <section className="case-studies-section" data-aos="fade-up">
          <h2>{text.caseTitle}</h2>
          <div className="case-studies-list">
            {text.caseStudies.map((item, idx) => (
              <div className="case-study-card" key={idx}>
                <div className="icon-title"><span className="icon">🎯</span><h3>{lang === 'ar' ? 'التحدي' : 'Challenge'}</h3></div>
                <p>{item.challenge}</p>
                <div className="icon-title"><span className="icon">🛠️</span><h3>{lang === 'ar' ? 'الحل' : 'Solution'}</h3></div>
                <p>{item.solution}</p>
                <div className="icon-title"><span className="icon">📈</span><h3>{lang === 'ar' ? 'النتيجة' : 'Result'}</h3></div>
                <p>{item.result}</p>
              </div>
            ))}
          </div>
        </section>

        {/* ✅ BLOG SECTION (Interactive) */}
        <section className="blog-section" data-aos="fade-up">
          <h2>{text.blogTitle}</h2>
          <div className="blog-list">
            {text.blogArticles.map(article => (
              <div
                key={article.id}
                className={`blog-post ${openArticle === article.id ? 'open' : ''}`}
                onClick={() => setOpenArticle(openArticle === article.id ? null : article.id)}
              >
                <h3>
                  <FaBookOpen /> {article.title}
                  <span className="read-more">
                    {openArticle === article.id
                      ? lang === 'ar' ? ' إغلاق' : ' Close'
                      : lang === 'ar' ? ' اقرأ المزيد' : ' Read more'}
                    <FaArrowRight />
                  </span>
                </h3>
                <p>{openArticle === article.id ? article.content : article.summary}</p>
              </div>
            ))}
          </div>
        </section>

        {/* ✅ CONTACT CTA */}
        <section className="contact-cta" data-aos="fade-up">
          <h3>{text.contactCta}</h3>
          <Link to="/contact" className="cta-button">{lang === 'ar' ? "تواصل معنا" : "Contact Us"}</Link>
        </section>

        {/* ✅ FLOATING CTA */}
        <Link to="/contact" className="floating-cta">
          {lang === 'ar' ? '📞 احجز استشارة' : '📞 Book a Call'}
        </Link>

        {/* ✅ TESTIMONIALS */}
        <section className="testimonials-section" data-aos="fade-left">
          <h2>{text.testimonialsTitle}</h2>
          <div className="testimonials">
            <div className="testimonial">
              <p>“{lang === 'ar'
                ? "تعامل احترافي وسرعة في الإنجاز. أوصي بهم بشدة."
                : "Professional and fast. Highly recommended!"}”</p>
              <strong>- {lang === 'ar' ? "أحمد، شركة ناشئة" : "Ahmed, Startup"}</strong>
            </div>
            <div className="testimonial">
              <p>“{lang === 'ar'
                ? "SmartTech AI قدمت لنا حلولًا ذكية حسّنت أداء أعمالنا."
                : "SmartTech AI gave us smart solutions that improved performance."}”</p>
              <strong>- {lang === 'ar' ? "مصطفى، مدير تسويق" : "Mustafa, Marketing Manager"}</strong>
            </div>
          </div>
        </section>

        {/* ✅ FAQ */}
        <section className="faq-section" data-aos="fade-up">
          <h2>{text.faqTitle}</h2>
          <div className="faq">
            <details>
              <summary>{lang === 'ar' ? 'كم يستغرق تنفيذ مشروع؟' : 'How long does a project take?'}</summary>
              <p>{lang === 'ar' ? 'يعتمد على حجم المشروع، لكن عادة من أسبوع إلى 6 أسابيع.' : 'Typically 1–6 weeks depending on size.'}</p>
            </details>
            <details>
              <summary>{lang === 'ar' ? 'هل توفرون دعم فني مستمر؟' : 'Do you provide ongoing support?'}</summary>
              <p>{lang === 'ar' ? 'نعم، نوفر باقات دعم وصيانة شهرية.' : 'Yes, we offer monthly support plans.'}</p>
            </details>
            <details>
              <summary>{lang === 'ar' ? 'هل يمكن تخصيص الحلول؟' : 'Are solutions customizable?'}</summary>
              <p>{lang === 'ar' ? 'بكل تأكيد، نحن نخصص كل حل حسب احتياجاتك.' : 'Absolutely. Every solution is tailored.'}</p>
            </details>
          </div>
        </section>

        {/* ✅ MAP SECTION */}
        <Map
          showTitle={true}
          height="400px"
        />

      </div>
    </>
  );
}
